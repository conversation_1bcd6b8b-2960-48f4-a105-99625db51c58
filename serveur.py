import Pyro4

@Pyro4.expose
class MonServeur:
    def dire_hello(self, nom):
        return "Hello, " + nom
    def additionner(self, a, b):
        return a + b
    
def start_serveur():
    daemon = Pyro4.Daemon(host="***************")

    ns = Pyro4.locateNS()

    object = MonServeur()
    uri = daemon.register(object)
    ns.register("mon.serveur", uri)

    print("Serveur pret.")
    daemon.requestLoop()

if __name__ == '__main__':
    start_serveur()