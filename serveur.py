# Import de la bibliothèque Pyro4 pour la communication RPC (Remote Procedure Call)
import Pyro4

# Le décorateur @Pyro4.expose permet d'exposer cette classe aux clients distants
# Sans ce décorateur, la classe ne serait pas accessible via le r<PERSON>eau
@Pyro4.expose
class MonServeur:
    """
    Classe qui contient les méthodes que les clients peuvent appeler à distance
    """

    def dire_hello(self, nom):
        """
        Méthode qui retourne un message de salutation personnalisé

        Args:
            nom (str): Le nom de la personne à saluer

        Returns:
            str: Message de salutation formaté
        """
        return "Hello, " + nom

    def additionner(self, a, b):
        """
        Méthode qui effectue l'addition de deux nombres

        Args:
            a (int/float): Premier nombre
            b (int/float): Deuxième nombre

        Returns:
            int/float: La somme des deux nombres
        """
        return a + b

def start_serveur():
    """
    Fonction qui configure et démarre le serveur RPC
    """
    # Création d'un daemon Pyro4 qui écoute sur l'adresse IP spécifiée
    # Le daemon est responsable de recevoir et traiter les requêtes des clients
    daemon = Pyro4.Daemon(host="***************")

    # Localisation du serveur de noms (Name Server) Pyro4
    # Le serveur de noms agit comme un annuaire qui permet aux clients
    # de trouver les objets distants par leur nom
    ns = Pyro4.locateNS()

    # Création d'une instance de notre classe serveur
    object = MonServeur()

    # Enregistrement de l'objet dans le daemon
    # Cette opération retourne un URI (identifiant unique) pour l'objet
    uri = daemon.register(object)

    # Enregistrement de l'objet dans le serveur de noms avec un nom lisible
    # Les clients pourront utiliser "mon.serveur" pour localiser cet objet
    ns.register("mon.serveur", uri)

    # Message confirmant que le serveur est prêt à recevoir des requêtes
    print("Serveur pret.")

    # Démarrage de la boucle d'écoute du serveur
    # Cette boucle infinie attend et traite les requêtes des clients
    daemon.requestLoop()

# Point d'entrée du programme
# Cette condition s'assure que start_serveur() n'est appelée que si ce fichier
# est exécuté directement (pas importé comme module)
if __name__ == '__main__':
    start_serveur()